// 现代仪表盘组件
const { DashboardBaseService } = require('../../core/services/dashboard-base-service.js')
const { DashboardService } = require('../../core/services/dashboard-service.js')
const { WorkHistoryService } = require('../../core/services/work-history-service.js')
const { SimpleRealTimeIncome } = require('../../utils/real-time-income.js')
const incomeAdjustmentService = require('../../core/services/income-adjustment-service.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否自动刷新数据
    autoRefresh: {
      type: Boolean,
      value: true
    },
    
    // 刷新间隔 (毫秒)
    refreshInterval: {
      type: Number,
      value: 1000
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 状态栏高度
    statusBarHeight: 0,

    // 是否显示设置模态框
    showSettings: false,

    // 仪表盘配置
    dashboardConfig: {},

    // 小数位数选项
    decimalOptions: [
      { label: '0位小数', value: 0 },
      { label: '1位小数', value: 1 },
      { label: '2位小数', value: 2 },
      { label: '3位小数', value: 3 }
    ],
    selectedDecimalIndex: 3, // 默认3位小数

    // 当前时间
    currentTime: '',
    currentDate: null,
    currentDateString: '',
    
    // 工作履历相关
    currentWork: null,
    currentWorkId: null,
    hasWorkHistory: false,
    currentCompany: '',
    currentPosition: '',

    // 脱敏状态
    companyMasked: false,
    positionMasked: false,

    // 显示设置
    showCurrentWork: true,
    
    // 统计数据
    todayFishTime: '0.00', // 今日摸鱼时间
    todayWorkTime: '0.00', // 今日服役时间
    todayIncome: '0.00',   // 今日总收入（基础收入）
    workIncome: '0.00',    // 今日工作收入
    fishingIncome: '0.00', // 今日摸鱼收入

    // 收入调整相关
    todayExtraIncome: '0.00',  // 今日额外收入（格式化字符串）
    todayDeductions: '0.00',   // 今日扣款（格式化字符串）
    todayNetIncome: '0.00',    // 今日净收入
    todayExtraIncomeValue: 0,  // 今日额外收入（数字，用于判断）
    todayDeductionsValue: 0,   // 今日扣款（数字，用于判断）
    
    monthSalary: '0.00',   // 本月时薪
    yearIncome: '0.00',    // 今年收入
    workDuration: '0.00',  // 工作时长
    fishingCount: '0',     // 摸鱼次数

    nonWorkDays: '0',      // 距离非工作日
    nextHoliday: '0',      // 最近的假期
    payDay: '0',           // 距离发薪日期
    payDayName: '未设置',   // 发薪日名称
    workDays: '0',         // 你已入职
    
    // 倒计时相关
    countdown: {
      hours: '03',
      minutes: '19',
      seconds: '50',
      text: '距离下班还有'
    },
    
    // 进度条
    workProgress: 65, // 工作进度百分比
    countdownProgress: 0, // 倒计时进度百分比
    
    // 货币符号
    currencySymbol: '¥',
    
    // 当前状态
    currentStatus: {
      type: 'working',
      isWorking: true
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 获取窗口信息
      const windowInfo = wx.getWindowInfo()
      this.setData({
        statusBarHeight: windowInfo.statusBarHeight || 20
      })

      this.initializeComponent()
    },
    
    detached: function() {
      this.cleanup()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    initializeComponent: function() {

      // 初始化服务
      this.baseService = new DashboardBaseService()
      this.dashboardService = new DashboardService()
      this.workHistoryService = new WorkHistoryService()

      // 初始化简化实时收入计算器
      this.realTimeIncome = new SimpleRealTimeIncome()

      // 设置当前日期
      const today = new Date()
      this.setData({
        currentDate: today
      })

      // 加载数据
      this.loadData()

      // 启动自动刷新
      if (this.data.autoRefresh) {
        this.startAutoRefresh()
      }
    },

    /**
     * 加载数据
     */
    loadData: function() {
      this.loadUserSettings()
      this.loadCurrentWork()
      this.updateStatistics()
      this.updateCurrentTime()
    },

    /**
     * 加载用户设置
     */
    loadUserSettings: function() {
      try {
        const currencySymbol = this.baseService.getCurrencySymbol()

        // 从仪表盘配置中获取小数位数
        const dashboardService = new DashboardService()
        const dashboardConfig = dashboardService.getDashboardSettings('dashboard2')
        const decimalPlaces = dashboardConfig.incomeDecimalPlaces !== undefined ? dashboardConfig.incomeDecimalPlaces : 3

        // 设置实时收入计算器的小数位数
        if (this.realTimeIncome) {
          this.realTimeIncome.setDecimalPlaces(decimalPlaces)
        } else {
          console.warn('Dashboard2 实时收入计算器不存在，无法设置小数位数')
        }

        this.setData({
          currencySymbol: currencySymbol,
          decimalPlaces: decimalPlaces
        })
      } catch (error) {
        console.error('加载用户设置失败:', error)
        this.setData({
          currencySymbol: '¥',
          decimalPlaces: 3
        })
      }
    },

    /**
     * 加载当前工作履历
     */
    loadCurrentWork: function() {
      try {
        const hasWorkHistory = this.baseService.hasWorkHistory()

        if (!hasWorkHistory) {
          this.setData({
            hasWorkHistory: false,
            currentWork: null,
            currentWorkId: null,
            currentCompany: '',
            currentPosition: ''
          })
          return
        }

        const currentWork = this.baseService.getCurrentWork()

        // 获取仪表盘配置
        const dashboardConfig = this.baseService.getDashboardConfig('dashboard2')
        const showCurrentWork = dashboardConfig.showCurrentWork !== false // 默认显示

        this.setData({
          hasWorkHistory: true,
          currentWork: currentWork,
          currentWorkId: currentWork ? currentWork.id : null,
          // 分别存储公司和职位，便于单独脱敏
          currentCompany: currentWork ? currentWork.company : '',
          currentPosition: currentWork ? currentWork.position : '',
          // 显示设置
          showCurrentWork: showCurrentWork
        })
      } catch (error) {
        console.error('加载当前工作履历失败:', error)
        this.setData({
          hasWorkHistory: false,
          currentWork: null,
          currentWorkId: null,
          currentCompany: '',
          currentPosition: ''
        })
      }
    },

    /**
     * 更新统计数据
     */
    updateStatistics: function() {
      if (!this.data.hasWorkHistory || !this.data.currentWorkId) {
        // 没有工作履历时，设置默认值
        const today = this.data.currentDate || new Date()
        this.setData({
          todayWorkTime: '0.00',
          todayFishTime: '0.00',
          todayIncome: '0.00',
          workIncome: '0.00',
          fishingIncome: '0.00',
          todayExtraIncome: '0.00',
          todayDeductions: '0.00',
          todayNetIncome: '0.00',
          todayExtraIncomeValue: 0,
          todayDeductionsValue: 0,
          monthSalary: '0.00',
          yearIncome: '0.00',
          workDuration: '0.00',
          fishingCount: '0',
          nonWorkDays: this.calculateDaysToWeekend(today).toString(),
          nextHoliday: this.calculateDaysToNextHoliday(today).toString(),
          workDays: '0',
          payDay: '0',
          payDayName: '未设置'
        })
        return
      }

      try {
        const today = this.data.currentDate
        
        // 获取今日数据
        const todayData = this.baseService.getTodayData(today, this.data.currentWorkId)
        
        // 获取月度和年度数据
        const monthlyStats = this.baseService.getMonthlyStats(today, this.data.currentWorkId)
        const yearlyStats = this.baseService.getYearlyStats(today, this.data.currentWorkId)
        
        // 计算时薪
        const monthSalary = this.baseService.calculateHourlyRate(
          monthlyStats.totalIncome, 
          monthlyStats.totalWorkMinutes
        )

        // 使用实时收入计算器更新今日收入
        if (this.realTimeIncome) {
          // 这里可以根据需要更新实时收入的配置
          // 实时收入的更新在updateCurrentTime方法中处理
        }

        // 计算发薪日倒计时
        const payDayInfo = this.workHistoryService.getNextPayDayInfo(this.data.currentWorkId)

        // 获取今日收入调整数据
        const adjustmentSummary = incomeAdjustmentService.getDayAdjustmentSummary(
          today,
          this.data.currentWorkId
        )

        // 计算净收入
        const baseIncome = todayData.dailyIncome || 0
        const extraIncome = adjustmentSummary.extraIncome || 0
        const deductions = adjustmentSummary.deductions || 0
        const netIncome = baseIncome + extraIncome - deductions

        // 计算摸鱼次数（今日摸鱼记录数量）
        const fishingCount = todayData.fishes ? todayData.fishes.length : 0

        // 计算摸鱼总时长（分钟）
        let fishingMinutes = 0
        if (todayData.fishes) {
          todayData.fishes.forEach(fish => {
            fishingMinutes += fish.end - fish.start
          })
        }

        // 计算距离非工作日的天数（距离周末）
        const nonWorkDays = this.calculateDaysToWeekend(today)

        // 计算距离下一个假期的天数
        const nextHoliday = this.calculateDaysToNextHoliday(today)

        // 计算入职天数
        const workDays = this.calculateWorkDays(this.data.currentWork)

        this.setData({
          todayWorkTime: this.baseService.formatAmount(todayData.workMinutes / 60, 'dashboard2'),
          todayFishTime: this.baseService.formatAmount(fishingMinutes / 60, 'dashboard2'),
          todayIncome: this.baseService.formatAmount(baseIncome, 'dashboard2'),
          todayExtraIncome: this.baseService.formatAmount(extraIncome, 'dashboard2'),
          todayDeductions: this.baseService.formatAmount(deductions, 'dashboard2'),
          todayNetIncome: this.baseService.formatAmount(netIncome, 'dashboard2'),
          // 新增：数字类型的判断字段
          todayExtraIncomeValue: extraIncome,  // 数字类型，用于判断是否显示
          todayDeductionsValue: deductions,    // 数字类型，用于判断是否显示
          monthSalary: this.baseService.formatAmount(monthSalary, 'dashboard2'),
          yearIncome: this.baseService.formatAmount(yearlyStats.totalIncome, 'dashboard2'),
          workDuration: this.baseService.formatAmount(todayData.workMinutes / 60, 'dashboard2'),
          fishingCount: fishingCount.toString(),
          nonWorkDays: nonWorkDays.toString(),
          nextHoliday: nextHoliday.toString(),
          workDays: workDays.toString(),
          payDay: payDayInfo.days.toString(),
          payDayName: payDayInfo.payDayName
        })
      } catch (error) {
        console.error('更新统计数据失败:', error)
      }
    },

    /**
     * 计算距离周末的天数
     * @param {Date} date 当前日期
     * @returns {number} 距离周末的天数
     */
    calculateDaysToWeekend: function(date) {
      const dayOfWeek = date.getDay() // 0=周日, 1=周一, ..., 6=周六
      if (dayOfWeek === 0) return 0 // 今天是周日
      if (dayOfWeek === 6) return 1 // 今天是周六，明天是周日
      return 6 - dayOfWeek // 距离周六的天数
    },

    /**
     * 计算距离下一个假期的天数
     * @param {Date} date 当前日期
     * @returns {number} 距离下一个假期的天数
     */
    calculateDaysToNextHoliday: function(date) {
      const currentYear = date.getFullYear()
      const holidays = [
        new Date(currentYear, 0, 1),   // 元旦
        new Date(currentYear, 4, 1),   // 劳动节
        new Date(currentYear, 9, 1),   // 国庆节
        new Date(currentYear + 1, 0, 1) // 明年元旦
      ]

      // 找到下一个假期
      for (let holiday of holidays) {
        if (holiday > date) {
          const timeDiff = holiday - date
          return Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
        }
      }

      return 0
    },

    /**
     * 计算入职天数
     * @param {Object} currentWork 当前工作信息
     * @returns {number} 入职天数
     */
    calculateWorkDays: function(currentWork) {
      if (!currentWork || !currentWork.startDate) {
        return 0
      }

      const startDate = new Date(currentWork.startDate)
      const today = new Date()
      const timeDiff = today - startDate
      return Math.max(0, Math.floor(timeDiff / (1000 * 60 * 60 * 24)))
    },

    /**
     * 更新当前时间和倒计时
     */
    updateCurrentTime: function() {
      const now = new Date()
      const timeString = now.toTimeString().substring(0, 8)

      // 格式化日期字符串
      const year = now.getFullYear()
      const month = (now.getMonth() + 1).toString().padStart(2, '0')
      const day = now.getDate().toString().padStart(2, '0')
      const dateString = `${year}-${month}-${day}`

      this.setData({
        currentTime: timeString,
        currentDateString: dateString
      })

      // 更新倒计时
      this.updateCountdown(now)

      // 更新工作进度
      this.updateWorkProgress(now)

      // 更新实时收入（如果有工作履历
      if (this.data.hasWorkHistory && this.data.currentWorkId) {
        // 使用简化的实时收入计算器
        if (!this.realTimeIncome) {
          console.error('Dashboard2 实时收入计算器不存在')
          return
        }

        const currentWork = this.data.currentWork
        const todayData = this.baseService.getTodayData(this.data.currentDate, this.data.currentWorkId)
        // 启动实时更新
        this.realTimeIncome.start(
          (result) => {
            this.setData({
              todayIncome: result.formattedIncome,
              workIncome: result.formattedWorkIncome,
              fishingIncome: result.formattedFishingIncome
            })
          },
          {
            workData: todayData,
            currentWork: currentWork,
            baseService: this.baseService,
            getFishingState: () => {
              // 获取摸鱼状态的函数
              const dataManager = require('../../core/managers/data-manager.js')
              return dataManager.getCurrentFishingState()
            }
          }
        )
      } else {
        // 停止实时更新
        if (this.realTimeIncome) {
          this.realTimeIncome.stop()
        }
      }

      // 定期更新统计数据（每分钟更新一次）
      if (!this.lastStatsUpdate || (now - this.lastStatsUpdate) > 60000) {
        this.updateStatistics()
        this.lastStatsUpdate = now
      }
    },

    /**
     * 更新倒计时
     */
    updateCountdown: function(now) {
      const countdown = this.baseService.calculateOffWorkCountdown(now, 18)

      // 计算倒计时进度（从100%到0%）
      const totalSecondsInDay = 9 * 60 * 60 // 9小时工作日
      const currentSeconds = parseInt(countdown.hours) * 3600 + parseInt(countdown.minutes) * 60 + parseInt(countdown.seconds)
      const countdownProgress = Math.max(0, Math.min(100, ((totalSecondsInDay - currentSeconds) / totalSecondsInDay) * 100))

      this.setData({
        countdown: countdown,
        countdownProgress: countdownProgress
      })
    },

    /**
     * 更新工作进度
     */
    updateWorkProgress: function(now) {
      const progress = this.baseService.calculateWorkProgress(now, 9, 18)
      
      this.setData({
        workProgress: progress
      })
    },

    /**
     * 启动自动刷新
     */
    startAutoRefresh: function() {
      this.refreshTimer = setInterval(() => {
        this.updateCurrentTime()
      }, this.data.refreshInterval)
    },

    /**
     * 停止自动刷新
     */
    stopAutoRefresh: function() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    /**
     * 清理资源
     */
    cleanup: function() {
      this.stopAutoRefresh()

      // 停止实时收入更新
      if (this.realTimeIncome) {
        this.realTimeIncome.stop()
      }
    },

    /**
     * 摸鱼开始事件处理
     */
    onFishingStart: function(e) {
      console.log('摸鱼开始:', e.detail)
      // 刷新数据以更新实时收入显示
      this.updateCurrentTime()
    },

    /**
     * 摸鱼结束事件处理
     */
    onFishingEnd: function(e) {
      console.log('摸鱼结束:', e.detail)
      // 刷新数据以更新统计信息
      this.updateStatistics()
      this.updateCurrentTime()
    },

    /**
     * 跳转到日历页面
     */
    goToCalendar: function() {
      this.triggerEvent('navigate', {
        url: '/pages/calendar/index',
        type: 'switchTab'
      })
    },

    /**
     * 跳转到工作履历页面
     */
    goToWorkHistory: function() {
      this.triggerEvent('navigate', {
        url: '/pages/work-history/index',
        type: 'switchTab'
      })
    },

    /**
     * 切换公司名脱敏显示
     */
    toggleCompanyMask: function() {
      this.setData({
        companyMasked: !this.data.companyMasked
      })
    },

    /**
     * 切换职位脱敏显示
     */
    togglePositionMask: function() {
      this.setData({
        positionMasked: !this.data.positionMasked
      })
    },

    /**
     * 外部调用的数据刷新方法
     */
    refreshData: function() {
      // 重新加载所有数据
      this.loadUserSettings()
      this.loadCurrentWork()
      this.updateStatistics()
      this.updateCurrentTime()
    },

    /**
     * 显示仪表盘切换器
     */
    onShowDashboardSwitcher: function() {
      this.triggerEvent('showDashboardSwitcher')
    },

    /**
     * 处理仪表盘切换事件
     */
    onDashboardChange: function(event) {
      const { dashboardId } = event.detail
      console.log('Dashboard2 收到仪表盘切换事件:', dashboardId)

      // 向父组件传递事件
      this.triggerEvent('dashboardChange', {
        dashboardId: dashboardId
      })
    },

    /**
     * 显示设置模态框
     */
    onShowSettings: function() {
      console.log('显示 Dashboard2 设置')

      // 加载当前配置
      this.loadDashboardConfig()

      this.setData({
        showSettings: true
      })
    },

    /**
     * 隐藏设置模态框
     */
    onHideSettings: function() {
      this.setData({
        showSettings: false
      })
    },

    /**
     * 模态框内容点击（阻止冒泡）
     */
    onModalContentTap: function() {
      // 阻止事件冒泡，防止关闭模态框
    },

    /**
     * 加载仪表盘配置
     */
    loadDashboardConfig: function() {
      try {
        const dashboardService = new DashboardService()
        const config = dashboardService.getDashboardSettings('dashboard2')

        // 加载小数位数设置
        const decimalPlaces = config.incomeDecimalPlaces !== undefined ? config.incomeDecimalPlaces : 3
        const selectedDecimalIndex = this.data.decimalOptions.findIndex(option => option.value === decimalPlaces)

        this.setData({
          dashboardConfig: config,
          selectedDecimalIndex: selectedDecimalIndex >= 0 ? selectedDecimalIndex : 3
        })
      } catch (error) {
        console.error('加载 Dashboard2 配置失败:', error)
      }
    },

    /**
     * 配置项变化
     */
    onConfigChange: function(event) {
      const key = event.currentTarget.dataset.key
      let value = event.detail.value

      const newConfig = Object.assign({}, this.data.dashboardConfig)
      newConfig[key] = value

      this.setData({
        dashboardConfig: newConfig
      })

      console.log('Dashboard2 配置项更新:', key, value)
    },

    /**
     * 小数位数变化
     */
    onDecimalChange: function(event) {
      const selectedIndex = event.detail.value
      const decimalPlaces = this.data.decimalOptions[selectedIndex].value

      console.log('Dashboard2 小数位数变化:', decimalPlaces)

      // 更新配置
      const newConfig = Object.assign({}, this.data.dashboardConfig)
      newConfig.incomeDecimalPlaces = decimalPlaces

      this.setData({
        selectedDecimalIndex: selectedIndex,
        dashboardConfig: newConfig
      })
    },

    /**
     * 保存设置
     */
    onSaveSettings: function() {
      console.log('保存 Dashboard2 设置:', this.data.dashboardConfig)

      try {
        const dashboardService = new DashboardService()
        dashboardService.updateDashboardConfig('dashboard2', this.data.dashboardConfig)

        // 如果小数位数发生变化，重新加载用户设置以更新显示
        this.loadUserSettings()

        // 重新加载数据以应用新的小数位数
        this.loadData()

        // 隐藏模态框
        this.setData({
          showSettings: false
        })

        // 显示成功提示
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1000
        })

      } catch (error) {
        console.error('保存 Dashboard2 设置失败:', error)
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    }
  }
})
