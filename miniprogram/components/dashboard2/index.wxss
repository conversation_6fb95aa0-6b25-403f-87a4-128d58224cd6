/* 现代仪表盘组件样式 */
.dashboard2-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  padding: 20rpx;
  padding-top: 152rpx; /* 为导航栏留出空间 */
}

/* Dashboard2 导航栏 */
.dashboard2-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.dashboard2-navbar .status-bar {
  width: 100%;
  background: transparent;
}

.dashboard2-navbar .navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  position: relative;
}

.dashboard2-navbar .navbar-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16rpx;
  min-width: 144rpx; /* 容纳两个按钮 */
  height: 100%;
}

.dashboard2-navbar .navbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 120rpx;
  height: 100%;
}

.dashboard2-navbar .navbar-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2rpx;
}

.dashboard2-navbar .navbar-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3436;
  text-align: center;
}

.datetime-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rpx;
}

.current-date {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
}

.current-time {
  font-size: 18rpx;
  color: #888;
  font-weight: 400;
  font-family: 'Courier New', monospace;
}

.dashboard2-navbar .navbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: rgba(45, 52, 54, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.dashboard2-navbar .navbar-button:active {
  transform: scale(0.95);
  background: rgba(45, 52, 54, 0.3);
}

.dashboard2-navbar .button-icon {
  font-size: 32rpx;
  color: #2d3436;
}

/* 引导界面样式 */
.no-work-guide {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.guide-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  max-width: 600rpx;
  width: 100%;
}

.guide-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.guide-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
}

.guide-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 48rpx;
}

.guide-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.guide-btn:active {
  transform: scale(0.96);
  box-shadow: 0 6rpx 24rpx rgba(102, 126, 234, 0.5);
}

.btn-icon {
  font-size: 28rpx;
}

/* Dashboard2 设置模态框 */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

.settings-content {
  width: 680rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.settings-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.settings-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 32rpx;
  color: #999999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.settings-close:active {
  background: #f0f0f0;
}

.settings-body {
  padding: 20rpx 40rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 32rpx;
  color: #333333;
  flex: 1;
}

.picker-value {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  min-width: 120rpx;
  text-align: center;
}

.settings-actions {
  display: flex;
  padding: 30rpx 40rpx;
  border-top: 2rpx solid #f0f0f0;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.cancel {
  background: #f8f9fa;
  color: #666666;
}

.action-btn.cancel:active {
  background: #e9ecef;
}

.action-btn.confirm {
  background: #fab1a0;
  color: #2d3436;
}

.action-btn.confirm:active {
  background: #e17055;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding: 20rpx 0;
  min-height: calc(100vh - 152rpx); /* 减去导航栏高度 */
}

/* 当前工作显示 */
.current-work-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 16rpx 20rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  margin-bottom: 16rpx;
}

.work-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.work-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.work-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.work-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.work-company,
.work-position {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.work-company:active,
.work-position:active {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(0.98);
}

.work-label {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
  min-width: 60rpx;
}

.work-value {
  font-size: 24rpx;
  color: #1a1a1a;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.mask-toggle {
  font-size: 20rpx;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

/* 顶部统计卡片 */
.top-stats {
  display: flex;
  gap: 12rpx;
  justify-content: space-between;
}

.stat-card {
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 20rpx;
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden; /* 防止内容溢出 */
}

.stat-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.12);
}

.stat-card .stat-icon {
  font-size: 36rpx;
  flex-shrink: 0; /* 图标不收缩 */
}

.stat-card .stat-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  min-width: 0; /* 允许内容收缩 */
  flex: 1;
}

.stat-card .stat-value {
  font-size: 24rpx;
  font-weight: 700;
  color: #1a1a1a;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-card .stat-label {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

/* 中央圆形进度条 */
.center-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 20rpx;
}

.countdown-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.countdown-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-part {
  font-size: 64rpx;
  font-weight: 700;
  color: #ff6b6b;
  font-family: 'Courier New', monospace;
}

.time-separator {
  font-size: 48rpx;
  font-weight: 700;
  color: #ff6b6b;
  opacity: 0.7;
}

.countdown-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.progress-text {
  font-size: 20rpx;
  color: #ff6b6b;
  font-weight: 600;
  opacity: 0.8;
}

/* 底部统计网格 */
.bottom-stats {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.stats-row {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.stat-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.12);
}

.stat-item .stat-icon {
  font-size: 32rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 16rpx;
}

.stat-item .stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stat-item .stat-value {
  font-size: 26rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.stat-item .stat-label {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
}

/* 收入统计卡片组 */
.income-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.income-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 16rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-height: 80rpx;
}

.income-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.income-icon {
  font-size: 32rpx;
  flex-shrink: 0;
}

.income-info {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
  min-width: 0;
  flex: 1;
}

.income-value {
  font-size: 22rpx;
  font-weight: 700;
  color: #1a1a1a;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.income-label {
  font-size: 18rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

/* 不同类型收入卡片的特殊样式 */
.work-income .income-icon {
  background: rgba(34, 197, 94, 0.1);
  border-radius: 12rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.work-income .income-value {
  color: #22c55e;
}

.fishing-income .income-icon {
  background: rgba(251, 191, 36, 0.1);
  border-radius: 12rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fishing-income .income-value {
  color: #fbbf24;
}

.extra-income .income-icon {
  background: rgba(168, 85, 247, 0.1);
  border-radius: 12rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.extra-income .income-value {
  color: #a855f7;
}

.deduction-income .income-icon {
  background: rgba(239, 68, 68, 0.1);
  border-radius: 12rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.deduction-income .income-value {
  color: #ef4444;
}

.breakdown-value.work {
  color: #4ade80;
}

.breakdown-value.fishing {
  color: #fbbf24;
}

.breakdown-value.extra {
  color: #22c55e;
}

.breakdown-value.deduction {
  color: #ef4444;
}

/* 摸鱼控制区域 */
.fishing-control-section {
  margin: 20rpx 0;
  padding: 0 4rpx;
}

/* 美化摸鱼控制组件 */
.fishing-control-section .fishing-control {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.fishing-control-section .control-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  min-width: 200rpx;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fishing-control-section .control-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.fishing-control-section .start-btn {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  box-shadow: 0 6rpx 20rpx rgba(34, 197, 94, 0.3);
}

.fishing-control-section .end-btn {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  box-shadow: 0 6rpx 20rpx rgba(239, 68, 68, 0.3);
}

.stat-item .stat-mascot {
  font-size: 32rpx;
  opacity: 0.6;
}
